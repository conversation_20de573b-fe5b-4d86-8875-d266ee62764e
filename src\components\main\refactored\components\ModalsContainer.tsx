import React from 'react';
import { Modal } from 'antd';
import Settingsatellite from '../../../Header/settingsatellite/settingsatellite';
import AddSatellite from '../../../Header/addsatelite';
import AddGroundStation from '../../../Header/addGroundStation';
import MappingGraph from '../../../Header/mappingGraph';
import UserPreferences from '../../../Header/userPreferences';
import { ModalStates, ModalActions } from '../hooks/useModalStates';
import { CesiumViewerState, CesiumViewerActions } from '../hooks/useCesiumViewer';
import { SimulationState } from '../hooks/useSimulationState';
import { clearAllCzmlData, loadCzmlData } from '../../../utils/czmlfunction';

// Cesium 是全局变量，通过 public/index.html 中的 script 标签加载
declare const Cesium: any;

interface ModalsContainerProps {
  modalStates: ModalStates & ModalActions;
  cesiumState: CesiumViewerState & CesiumViewerActions;
  simulationState: SimulationState & any;
}

/**
 * 弹窗容器组件
 * 负责管理所有弹窗的渲染
 */
const ModalsContainer: React.FC<ModalsContainerProps> = ({ 
  modalStates, 
  cesiumState, 
  simulationState 
}) => {
  const {
    isConstellationSettingOpen,
    isAddSatelliteOpen,
    isAddGroundStationOpen,
    isMappingModalOpen,
    isUserPreferencesOpen,
    closeConstellationSettingPanel,
    closeAddSatellitePanel,
    closeAddGroundStationPanel,
    closeMappingModal,
    closeUserPreferences,
    handleAddSatelliteSubmit,
    handleAddGroundStationSubmit,
  } = modalStates;

  const {
    viewer,
    satelliteList,
    setSatelliteList,
    setBaseStationList,
    setCurSatellite,
    setCurBaseStation,
    beamCellManager,
  } = cesiumState;

  const {
    setting,
    setSetting,
    sceneList,
    setSceneList,
    networkModeConfig,
    kvmList,
    setSimulationConstellationName,
    setSimulationRunning,
    resetSimulation,
    setIsLoading,
    setCurrentSatelliteName,
  } = simulationState;

  // 清除所有CZML数据的包装函数
  const clearAllCzmlDataWrapper = () => {
    const clearParams = {
      viewer,
      setSatelliteList,
      setBaseStationList,
      setCurSatellite,
      setCurBaseStation,
      resetSimulation,
      beamCellManager
    };
    clearAllCzmlData(clearParams);
  };

  // 加载CZML数据的包装函数
  const loadCzmlDataWrapper = async (satelliteName: string, positionInfo?: any) => {
    const czmlDataSource = new Cesium.CzmlDataSource();
    const loadParams = {
      viewer,
      czmlDataSource,
      setIsLoading,
      setSimulationConstellationName,
      setBaseStationList,
      setSatelliteList,
      setSimulationRunning
    };
    return loadCzmlData(satelliteName, loadParams, positionInfo);
  };

  // 处理卫星名称变化
  const handleSatelliteNameChange = (satelliteName: string) => {
    setCurrentSatelliteName(satelliteName);
  };

  return (
    <>
      {/* 星座配置面板Modal */}
      <Modal
        title="星座选择配置"
        className="sceneEdit"
        visible={isConstellationSettingOpen}
        onCancel={closeConstellationSettingPanel}
        footer={null}
        centered
      >
        <Settingsatellite 
          onClearCzmlData={clearAllCzmlDataWrapper}
          setting={setting} 
          setSetting={setSetting} 
          satelliteList={satelliteList} 
          setSatelliteList={setSatelliteList} 
          setScanes={setSceneList}
          onSatelliteNameChange={handleSatelliteNameChange}
          onLoadCzmlData={loadCzmlDataWrapper}
        />
      </Modal>
      
      {/* 星座设计面板Modal */}
      <AddSatellite
        visible={isAddSatelliteOpen}
        onClose={closeAddSatellitePanel}
        onSubmit={handleAddSatelliteSubmit}
      />
      
      {/* 地面站设计面板Modal */}
      <AddGroundStation
        visible={isAddGroundStationOpen}
        onClose={closeAddGroundStationPanel}
        onSubmit={handleAddGroundStationSubmit}
      />
      
      {/* 映射图弹窗 */}
      <MappingGraph
        visible={isMappingModalOpen}
        onClose={closeMappingModal}
        networkModeConfig={networkModeConfig}
        kvmList={kvmList}
      />

      {/* 用户首选项面板Modal */}
      <UserPreferences
        visible={isUserPreferencesOpen}
        onClose={closeUserPreferences}
      />
    </>
  );
};

export default ModalsContainer;
