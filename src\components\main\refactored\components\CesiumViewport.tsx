import React, { useEffect } from 'react';
import { CesiumViewerState, CesiumViewerActions } from '../hooks/useCesiumViewer';
import { SimulationState } from '../hooks/useSimulationState';
import '../../css/cesium.css';

interface CesiumViewportProps {
  cesiumState: CesiumViewerState & CesiumViewerActions;
  simulationState: SimulationState & any;
}

/**
 * Cesium 3D视图容器组件
 * 负责渲染Cesium容器和初始化
 */
const CesiumViewport: React.FC<CesiumViewportProps> = ({ 
  cesiumState, 
  simulationState 
}) => {
  const { initializeCesium, cleanupCesium, isInit } = cesiumState;
  const { startTimeSync, stopTimeSync } = simulationState;

  // 初始化Cesium - 只在组件挂载时执行一次
  useEffect(() => {
    initializeCesium();

    return () => {
      cleanupCesium();
    };
  }, []); // 空依赖数组，只在挂载时执行一次

  // 启动时间同步
  useEffect(() => {
    if (cesiumState.viewer && isInit) {
      startTimeSync(cesiumState.viewer);
    }
    
    return () => {
      stopTimeSync();
    };
  }, [cesiumState.viewer, isInit, startTimeSync, stopTimeSync]);

  return (
    <div
      id="cesiumContainer"
      style={{
        height: "100%",
        width: "100%",
        background: "#000",
      }}
    />
  );
};

export default CesiumViewport;
