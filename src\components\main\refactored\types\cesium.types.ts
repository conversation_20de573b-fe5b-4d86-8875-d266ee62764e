/**
 * Cesium相关类型定义
 */

// Cesium Viewer类型定义
export interface CesiumViewer {
  scene: CesiumScene;
  camera: CesiumCamera;
  clock: CesiumClock;
  homeButton: CesiumHomeButton;
  sceneModePicker: CesiumSceneModePicker;
  imageryLayers: CesiumImageryLayerCollection;
  terrainProvider: any;
  resolutionScale: number;
  destroy: () => void;
  _cesiumWidget: {
    _supportsImageRenderingPixelated: boolean;
    _forceResize: boolean;
  };
}

// Cesium Scene类型定义
export interface CesiumScene {
  mode: number;
  skyBox: { show: boolean };
  sun: { show: boolean };
  moon: { show: boolean };
  backgroundColor: any;
  postProcessStages: CesiumPostProcessStages;
  primitives: CesiumPrimitiveCollection;
  debugShowFramesPerSecond: boolean;
}

// Cesium Camera类型定义
export interface CesiumCamera {
  setView: (options: CesiumCameraViewOptions) => void;
}

export interface CesiumCameraViewOptions {
  destination: any; // Cesium.Cartesian3
  orientation?: {
    heading: number;
    pitch: number;
    roll: number;
  };
}

// Cesium Clock类型定义
export interface CesiumClock {
  currentTime: CesiumJulianDate;
  startTime: CesiumJulianDate;
  shouldAnimate: boolean;
  multiplier: number;
  onTick: CesiumEvent;
}

// Cesium JulianDate类型定义
export interface CesiumJulianDate {
  secondsOfDay: number;
  toString: () => string;
}

// Cesium Event类型定义
export interface CesiumEvent {
  addEventListener: (listener: Function) => void;
  removeEventListener: (listener: Function) => void;
}

// Cesium Home Button类型定义
export interface CesiumHomeButton {
  viewModel: {
    duration: number;
    command: {
      afterExecute: CesiumEvent;
    };
  };
}

// Cesium Scene Mode Picker类型定义
export interface CesiumSceneModePicker {
  viewModel: {
    morphTo2D: { afterExecute: CesiumEvent };
    morphToColumbusView: { afterExecute: CesiumEvent };
    morphTo3D: { afterExecute: CesiumEvent };
  };
}

// Cesium Imagery Layer Collection类型定义
export interface CesiumImageryLayerCollection {
  get: (index: number) => CesiumImageryLayer;
  addImageryProvider: (provider: any) => void;
}

export interface CesiumImageryLayer {
  brightness: number;
}

// Cesium Post Process Stages类型定义
export interface CesiumPostProcessStages {
  remove: (stage: any) => void;
}

// Cesium Primitive Collection类型定义
export interface CesiumPrimitiveCollection {
  add: (primitive: any) => void;
}

// Cesium Handler类型定义
export interface CesiumHandler {
  setInputAction: (action: Function, type: any) => void;
  destroy: () => void;
}

// Cesium CZML Data Source类型定义
export interface CesiumCzmlDataSource {
  load: (czml: any) => Promise<void>;
  entities: CesiumEntityCollection;
}

export interface CesiumEntityCollection {
  values: CesiumEntity[];
  getById: (id: string) => CesiumEntity | undefined;
}

export interface CesiumEntity {
  id: string;
  name?: string;
  position?: any;
  billboard?: any;
  label?: any;
  polyline?: any;
  polygon?: any;
  show: boolean;
}

// Cesium 3D Tileset类型定义
export interface Cesium3DTileset {
  // 3D瓦片集相关属性
}

// Cesium Cartesian3类型定义
export interface CesiumCartesian3 {
  x: number;
  y: number;
  z: number;
}

// Cesium Color类型定义
export interface CesiumColor {
  red: number;
  green: number;
  blue: number;
  alpha: number;
}

// 鼠标事件相关类型
export interface CesiumMouseEvent {
  position: CesiumCartesian2;
  endPosition?: CesiumCartesian2;
}

export interface CesiumCartesian2 {
  x: number;
  y: number;
}

// 拾取结果类型
export interface CesiumPickResult {
  id: CesiumEntity;
  primitive?: any;
}

// Cesium常量类型
export interface CesiumConstants {
  SceneMode: {
    SCENE3D: number;
    SCENE2D: number;
    COLUMBUS_VIEW: number;
  };
  ScreenSpaceEventType: {
    LEFT_CLICK: any;
    RIGHT_CLICK: any;
    MOUSE_MOVE: any;
    WHEEL: any;
  };
  Math: {
    PI_OVER_TWO: number;
  };
  FeatureDetection: {
    supportsImageRenderingPixelated: () => boolean;
  };
  JulianDate: {
    secondsDifference: (end: CesiumJulianDate, start: CesiumJulianDate) => number;
  };
  Cartesian3: {
    fromDegrees: (longitude: number, latitude: number, height?: number) => CesiumCartesian3;
  };
  Ion: {
    defaultAccessToken: string;
  };
}
