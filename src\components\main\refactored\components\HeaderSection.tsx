import React from 'react';
import Header from '../../../Header';
import { ModalStates, ModalActions } from '../hooks/useModalStates';
import { SimulationState } from '../hooks/useSimulationState';

interface HeaderSectionProps {
  modalStates: ModalStates & ModalActions;
  simulationState: SimulationState & any;
}

/**
 * 头部导航组件
 * 负责渲染顶部导航栏
 */
const HeaderSection: React.FC<HeaderSectionProps> = ({ 
  modalStates, 
  simulationState 
}) => {
  const {
    showConstellationSettingPanel,
    showAddSatellitePanel,
    showAddGroundStationPanel,
    showMappingModal,
    showUserPreferences,
  } = modalStates;

  const { setSituation } = simulationState;

  return (
    <Header
      setSituation={setSituation}
      showConstellationSettingPanel={showConstellationSettingPanel}
      satelliteAdd={showAddSatellitePanel}
      groundStationAdd={showAddGroundStationPanel}
      showMappingModal={showMappingModal}
      showUserPreferences={showUserPreferences}
    />
  );
};

export default HeaderSection;
