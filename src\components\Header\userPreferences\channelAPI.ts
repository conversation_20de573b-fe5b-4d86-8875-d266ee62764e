// 信道模型API接口
import { basisBackendUrl } from '../../utils/api/basicURL';

const BACKEND_URL = basisBackendUrl; // 使用项目统一的后端地址配置

/**
 * 自定义信道数据类型定义
 */
export interface CustomChannelData {
  channel_name: string;
  path_loss_exponent?: number;
  shadowing_std?: number;
  fading_factor?: number;
  additional_loss?: number;
  custom_params?: {
    environment?: string;
    frequency?: number;
    [key: string]: any;
  };
}

/**
 * 信道参数数据类型定义
 */
export interface ChannelParamsData {
  channel_type: string;
  path_loss_exponent?: number;
  shadowing_std?: number;
  fading_factor?: number;
  additional_loss?: number;
  custom_params?: {
    rain_attenuation?: number;
    atmospheric_loss?: number;
    [key: string]: any;
  };
}

/**
 * API响应类型定义
 */
export interface ApiResponse {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
  channel_type?: string;
  channel_types?: string[];
}

/**
 * 创建自定义信道模型
 * @param data 自定义信道数据
 * @returns Promise<ApiResponse>
 */
export async function createCustomChannel(data: CustomChannelData): Promise<ApiResponse> {
  try {
    const response = await fetch(`${BACKEND_URL}/beam/create_custom_channel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error: any) {
    console.error('创建自定义信道失败:', error);
    return {
      success: false,
      message: error.message || '创建自定义信道失败'
    };
  }
}

/**
 * 设置信道类型（基础接口）
 * @param channelType 信道类型名称
 * @returns Promise<ApiResponse>
 */
export async function setChannelType(channelType: string): Promise<ApiResponse> {
  try {
    const response = await fetch(`${BACKEND_URL}/api/beam/set_channel_type`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channel_type: channelType
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error: any) {
    console.error('设置信道类型失败:', error);
    return {
      success: false,
      message: error.message || '设置信道类型失败'
    };
  }
}

/**
 * 设置信道类型及参数（高级接口）
 * @param data 信道参数数据
 * @returns Promise<ApiResponse>
 */
export async function setChannelWithParams(data: ChannelParamsData): Promise<ApiResponse> {
  try {
    const response = await fetch(`${BACKEND_URL}/api/beam/set_channel_with_params`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error: any) {
    console.error('设置信道参数失败:', error);
    return {
      success: false,
      message: error.message || '设置信道参数失败'
    };
  }
}

/**
 * 获取当前信道类型
 * @returns Promise<ApiResponse>
 */
export async function getCurrentChannelType(): Promise<ApiResponse> {
  try {
    const response = await fetch(`${BACKEND_URL}/api/beam/get_channel_type`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error: any) {
    console.error('获取当前信道类型失败:', error);
    return {
      success: false,
      message: error.message || '获取当前信道类型失败'
    };
  }
}

/**
 * 获取所有可用信道类型
 * @returns Promise<ApiResponse>
 */
export async function getAvailableChannelTypes(): Promise<ApiResponse> {
  try {
    const response = await fetch(`${BACKEND_URL}/api/beam/get_available_channel_types`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error: any) {
    console.error('获取可用信道类型失败:', error);
    return {
      success: false,
      message: error.message || '获取可用信道类型失败'
    };
  }
}

/**
 * 删除自定义信道模型
 * @param channelName 要删除的信道名称
 * @returns Promise<ApiResponse>
 */
export async function deleteCustomChannel(channelName: string): Promise<ApiResponse> {
  try {
    const response = await fetch(`${BACKEND_URL}/api/beam/delete_custom_channel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channel_name: channelName
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error: any) {
    console.error('删除自定义信道失败:', error);
    return {
      success: false,
      message: error.message || '删除自定义信道失败'
    };
  }
}

/**
 * 验证信道数据
 * @param data 信道数据
 * @returns 验证结果
 */
export function validateChannelData(data: CustomChannelData): { isValid: boolean; error?: string } {
  if (!data.channel_name || data.channel_name.trim() === '') {
    return { isValid: false, error: '信道名称不能为空' };
  }

  if (data.path_loss_exponent !== undefined && (data.path_loss_exponent < 1.0 || data.path_loss_exponent > 6.0)) {
    return { isValid: false, error: '路径损耗指数必须在1.0到6.0之间' };
  }

  if (data.shadowing_std !== undefined && (data.shadowing_std < 0 || data.shadowing_std > 20)) {
    return { isValid: false, error: '阴影衰落标准差必须在0到20之间' };
  }

  if (data.fading_factor !== undefined && (data.fading_factor < 0 || data.fading_factor > 2)) {
    return { isValid: false, error: '衰落因子必须在0到2之间' };
  }

  if (data.additional_loss !== undefined && (data.additional_loss < 0 || data.additional_loss > 50)) {
    return { isValid: false, error: '额外损耗必须在0到50dB之间' };
  }

  return { isValid: true };
}
