import React from 'react';
import Box from '../../box';
import SatelliteInfo from '../../../right/satelliteInfo/satelliteInfo';
import CoreNetworkLogs from '../../../right/CoreNetwork/CoreNetworkLogs';
import { CesiumViewerState, CesiumViewerActions } from '../hooks/useCesiumViewer';
import { SimulationState } from '../hooks/useSimulationState';

interface RightPanelProps {
  cesiumState: CesiumViewerState & CesiumViewerActions;
  simulationState: SimulationState & any;
}

/**
 * 右侧面板组件
 * 负责渲染右侧的卫星信息和核心网日志
 */
const RightPanel: React.FC<RightPanelProps> = ({ 
  cesiumState, 
  simulationState 
}) => {
  const { curSatellite } = cesiumState;
  const { simulationConstellationName } = simulationState;

  return (
    <div className="right-wrap" style={{
      minWidth: 'max-content',
      right: '20px'
    }}>
      <Box
        title="卫星/地面站信息显示"
        component={
          <SatelliteInfo
            sateName={curSatellite.split(' ').pop() || ''}
            satelliteId={curSatellite.split(' ').pop() || ''}
            status={"service"}
            type={"satellite"}
            constellationName={simulationConstellationName}
          />
        }
      />
      <Box
        title="核心网日志"
        component={<CoreNetworkLogs/>}
      />
    </div>
  );
};

export default RightPanel;
