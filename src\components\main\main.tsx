// @ts-nocheck
import React, { useEffect, useState, useRef, useCallback } from "react";
import {Modal, Select} from "antd";
//@ts-ignore
// import * as Cesium from 'cesium/Cesium';
// antd CSS is imported globally in App.tsx
import "./css/cesium.css";
import {
  BaseStation,
  PolarEarthProps,
  SceneDataType,
  SceneType,
  SettingType,
  situationType,
} from "../types/type";
import BaseStationInfo from "./temp/baseStationInfo";

import Box from "./box";

import SatelliteBar from "../left/satelliteBar";
import BasestationHourChart from "../left/basestationHourChart";
import BasestationList from "../features/bottom/basestationList";
import SatelliteNumberChart from "../left/networkState/satelliteNumberChart";
import PolarEarth from "../right/palarEarth";
import SatelliteInfo from "../right/satelliteInfo/satelliteInfo";
import Basestation<PERSON>hart from "../right/basestationChart";
import BasestationBar from "../left/basestationBar";
import SatelliteInfoList from "../left/satelliteInfoList/satelliteInfoList";
import BaseChart0 from "../right/baseChart0";
import "./temp/LineFlowMaterialProperty";
import "./temp/Spriteline1MaterialProperty";
import { CesiumComponentType } from "../types/type";
import $ from 'jquery';
import SettingPanel from "./temp/settingPanel";
import Settingsatellite from "../Header/settingsatellite/settingsatellite";
import AddSatellite from "../Header/addsatelite";

import ResourcePanel from "../resource/resourceMain";

import SatelliteWorkTime from "../right/satelliteWorkTime";
import CoreNetworkLogs from "../right/CoreNetwork/CoreNetworkLogs";
import Camera from "../../assets/images/camera.svg";
import { generateId } from "../utils/tool";
import Header from "../Header";
import AddGroundStation from "../Header/addGroundStation";
import MappingGraph from "../Header/mappingGraph";
import UserPreferences from "../Header/userPreferences";

// 添加API导入
import { setSelectedConstellation, getTerminalPathCzml,postCurrentTime } from "../utils/api/postAPI";
import { getCZMLData } from "../utils/api/getListAPI";
import { getConstellationList } from "../utils/api/getListAPI";

// 添加 Zustand store 导入
import useSimulationStore from "../../store/simulationStore";

// 添加封装的函数导入
import { 
  clearAllCzmlData, 
  loadCzmlData, 
  type ClearCzmlDataParams, 
  type LoadCzmlDataParams, 
  type PositionInfo 
} from "../utils/czmlfunction";

// 添加波束小区整合功能导入
import {
  BeamCellManager,
  type BeamCellManagerParams
} from "../features/beamCell";

// 添加鼠标事件模块导入
import {
  setupMouseHandlers,
  type MouseHandlerDependencies
} from "../features/mouse";

// 添加设置相关函数导入
import {
  settingDeal,
  earthRotate,
  GetWGS84FromDKR,
  wgs84ToCartesign,
  type SettingDealParams,
  type EarthRotateParams
} from "../utils/setfunction";

const { Option } = Select;

// 设置CDN服务器位置，如果没有则使用相对路径
const cdnServerLocation = "./images/";

//@ts-ignore
let viewer: any;
let linkToBaseStation: any = {};
var handler: {
  setInputAction: (
    arg0: {
      (movement: { endPosition: any }): void;
      (movement: { position: any }): void;
      (): void;
    },
    arg1: any
  ) => void;
  destroy: () => void;
};

let rain: any, snow: any, fog: any;
let stages: any;
let previousTime: any;
let netCollection: any[] = [];
let timeID: any;
let timeID1: any;
let polarTimeId: any;
let clicked = false;
let timerOpen, timerClose;
let hexagon: any[] = [];
let isLoad: boolean = false
const CesiumComponent: React.FC<CesiumComponentType> = (props) => {
  const { setDashboard } = props;
  const [init, setInit] = useState<boolean>(false);
  const [isDrawLine, setIsDrawLine] = useState<boolean>(false);
  const [isDrawPolygon, setIsDrawPolygon] = useState<boolean>(false);
  const [isRotate, setIsRotate] = useState<boolean>(false);
  const [satellitePostionData, setSatellitePostionData] = useState<number[]>(
    []
  );
  const [nowSystemDate, setNowSystemDate] = useState<string[]>([]);
  // 卫星列表
  const [satelliteList, setSatelliteList] = useState<string[]>([]);
  const satelliteListRef = useRef(satelliteList);
  const [satelliteColor, setSatelliteColor] = useState({}); 

  const [baseStationList, setBaseStationList] = useState<BaseStation[]>([]);
  const [curSatellite, setCurSatellite] = useState<String>("");
  const [curBaseStation, setCurBaseStation] = useState<BaseStation | null>(
    null
  );
  const curBaseStationRef = useRef(curBaseStation);
  const [weatherIcon, setWeatherIcon] = useState<string>("0")
  const [polarPosition, setPolarPosition] = useState<PolarEarthProps>(null);
  


  // 监听态势情景变化
  const [situation, setSituation] = useState<situationType>({
    satellite: true,
    communicate: true,
    basestation: false,
    resource: false,
    business: false,
    current: ""
  });
  const [isShowNet, setIsShowNet] = useState<Boolean>(false);
  const [isShowBasestationNet, setIsShowBasestationNet] =
    useState<boolean>(false);
  const [groundBusinessState, setGroundBusiniessState] = useState<any>(null);
  const [groundReliabilityState, setGroundReliabilityState] =
    useState<any>(null);
  const [groundStabilityState, setGroundStabilityState] = useState<any>(null);
  // 设置数据
  const [setting, setSetting] = useState<SettingType>({
    label: { val: false, name: "卫星标注" },
    icon: { val: true, name: "卫星图标" },
    track: { val: false, name: "卫星轨迹" },
    light: { val: false, name: "显示光照" },
    sun: { val: true, name: "显示太阳" },
    star: { val: false, name: "显示星空" },
    time: { val: true, name: "显示时间轴" },
    rotate: { val: false, name: "地球旋转" },
  });

  // 添加控制星座配置面板显示的状态
  const [isConstellationSettingOpen, setIsConstellationSettingOpen] = useState<boolean>(false);
  // 添加控制星座设计面板显示的状态
  const [isAddSatelliteOpen, setIsAddSatelliteOpen] = useState<boolean>(false);
  // 添加控制地面站设计面板显示的状态
  const [isAddGroundStationOpen, setIsAddGroundStationOpen] = useState<boolean>(false);
  // 在顶部导入 useSimulationStore
  const [isMappingModalOpen, setIsMappingModalOpen] = useState(false);
  // 添加控制用户首选项面板显示的状态
  const [isUserPreferencesOpen, setIsUserPreferencesOpen] = useState<boolean>(false);


  const [sceneList, setScenList] = useState<SceneType[]>([]);
  // 当前选中的卫星信息
  const nowPicksatellite = useRef<any>(null);
  // 当前选中的卫星实体引用
  const selectedSatelliteEntity = useRef<any>(null);
  // 整合的波束小区管理器
  const beamCellManager = useRef<BeamCellManager | null>(null);

  // 添加高亮波束状态，记录当前哪个波束被高亮
  // const [highlightedBeam, setHighlightedBeam] = useState<{satelliteId: string, beamIndex: number} | null>(null);
  
  // 使用 Zustand store 管理仿真状态
  const {
    simulationRunning,
    isLoading,
    simulationConstellationName,
    currentSatelliteName,
    cesiumTime,
    timeDiff,
    networkModeConfig,
    kvmList,
    setSimulationRunning,
    setIsLoading,
    setSimulationConstellationName,
    setCurrentSatelliteName,
    setCesiumTime,
    setPickedObject,
    resetSimulation,
  } = useSimulationStore();


  const czmlDataSource = new Cesium.CzmlDataSource();
  
  useEffect(() => {
    setInit(true);
  }, []);

  useEffect(() => {
    if (viewer !== undefined) {
      if (isRotate) {
        viewer.clock.onTick.addEventListener(earthRotateWrapper);
      } else {
        viewer.clock.onTick.removeEventListener(earthRotateWrapper);
      }
    }
  }, [isRotate]);

  useEffect(() => {
    if (init) {
      console.log('Cesium初始化开始');
      
      // 添加对satelliteList的初始化验证
      console.log("验证satelliteList数据格式...");
      try {
        // 初始化卫星列表引用
        if (!Array.isArray(satelliteListRef.current)) {
          satelliteListRef.current = [];
          console.log("初始化satelliteListRef为空数组");
        }
      } catch (error) {
        console.error("satelliteList初始化验证出错:", error);
      }
      
      Cesium.Ion.defaultAccessToken =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.sfpT8e4oxun23JG--UmUN9ZD4SbQfU-Ljvh2MsPTTcY";
      viewer = new Cesium.Viewer("cesiumContainer", {
        shouldAnimate: true,
        infoBox: false, // 是否显示点击要素之后显示的信息
        // 去掉地球表面的大气效果黑圈问题
        orderIndependentTranslucency: true,
        // terrainProvider : Cesium.createWorldTerrain(),
        // terrainProvider: new Cesium.CesiumTerrainProvider({
        //   url: Cesium.IonResource.fromAssetId(1),
        // }),
        contextOptions: {
          webgl: {
            alpha: true,
          },
        },
        timeline: true,
        animation: true,
      });
      // const Melbourne_tileset = new Cesium.Cesium3DTileset({
      //   url: Cesium.IonResource.fromAssetId(69380),
      // });
      // const Washington_tileset = new Cesium.Cesium3DTileset({
      //   url: Cesium.IonResource.fromAssetId(57588),
      // });
      // const KangJuXinCheng_tileset = new Cesium.Cesium3DTileset({
      //   url: "./KangJuXinCheng-3dtiles/tileset.json"
      // })
      // viewer.scene.primitives.add(Melbourne_tileset);
      // viewer.scene.primitives.add(Washington_tileset);
      // viewer.scene.primitives.add(KangJuXinCheng_tileset);
      // // 添加高德影像图
      const imageryLayers = viewer.imageryLayers;
      //  // 显示帧率
      // viewer.scene.debugShowFramesPerSecond = true;

      // 添加地形数据
      // viewer.terrainProvider = Cesium.createWorldTerrain();

      // let imageryProvider = new Cesium.UrlTemplateImageryProvider({
      //   url: "https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
      //   layer: "tdtVecBasicLayer",
      //   style: "default",
      //   format: "image/png",
      //   tileMatrixSetID: "GoogleMapsCompatible",
      //   show: false,
      // });
      // imageryLayers.addImageryProvider(imageryProvider);

      stages = viewer.scene.postProcessStages;

      //  GetWGS84FromDKR(new Cesium.Cartesian3(-2180335.**********, 4388094.640359055, 4069400.047373593),1)

      //  place1
      // wgs84ToCartesign(
      //   116.42145182931263,
      //   39.8978797042195,
      //   0
      // )

      // place2
      // wgs84ToCartesign(
      //   144.92242809864345,
      //   -37.79837165450737,
      //   19
      // )

      // 背景切换为图片
      // 去掉黑色星空背景
      viewer.scene.skyBox.show = false;
      viewer.scene.sun.show = true;
      viewer.scene.moon.show = true;
      // viewer.scene.backgroundColor = new Cesium.Color(0.0, 0.0, 0.0, 0.0);

      // 尝试提高分辨率
      viewer._cesiumWidget._supportsImageRenderingPixelated =
        Cesium.FeatureDetection.supportsImageRenderingPixelated();
      viewer._cesiumWidget._forceResize = true;
      if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) {
        var vtxf_dpr = window.devicePixelRatio;
        // 适度降低分辨率
        while (vtxf_dpr >= 2.0) {
          vtxf_dpr /= 2.0;
        }
        //alert(dpr);
        viewer.resolutionScale = vtxf_dpr;
      }


      console.log("Cesium初始化完成，等待仿真开始...");
      

      // 初始化整合的波束小区管理器
      const beamCellManagerParams: BeamCellManagerParams = {
        viewer
      };
      beamCellManager.current = new BeamCellManager(beamCellManagerParams);
      console.log("整合波束小区管理器初始化完成");

      viewer.homeButton.viewModel.duration = 0;
      viewer.homeButton.viewModel.command.afterExecute.addEventListener(
        function (e) {
          setCurBaseStation(null);
          // 强制切换到3D模式
          if (viewer.scene.mode !== Cesium.SceneMode.SCENE3D) {
            viewer.scene.mode = Cesium.SceneMode.SCENE3D;
          }
          // 设置回到初始视角
          viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(0, 0, 20000000), // 地球正上方20000km
            orientation: {
              heading: 0.0,
              pitch: -Cesium.Math.PI_OVER_TWO, // 向下俯视地球
              roll: 0.0
            }
          });
          setIsRotate(false);
        }
      );

      // 设置鼠标事件处理器
      const mouseHandlerDependencies: MouseHandlerDependencies = {
        viewer,
        setCurSatellite,
        setNowSystemDate,
        setSatellitePostionData,
        wgs84ToCartesignWrapper,
        nowPicksatellite,
        setPickedObject,
        beamCellManager,
        selectedSatelliteEntity
      };
      
      // 使用封装的鼠标事件处理器
      const handler = setupMouseHandlers(mouseHandlerDependencies);

      // 设置初始视角
      viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(0, 0, 20000000), // 地球正上方20000km
        orientation: {
          heading: 0.0,
          pitch: -Cesium.Math.PI_OVER_TWO, // 向下俯视地球
          roll: 0.0
        }
      });

      // 地球旋转
      // viewer.clock.multiplier = 100; //速度
      viewer.clock.shouldAnimate = true;
      previousTime = viewer.clock.currentTime.secondsOfDay;
      setIsRotate(false);

      // 设置时钟事件监听器来更新store中的Cesium时间和小区显示
      viewer.clock.onTick.addEventListener(() => {
        updateCesiumTimeToStore();

        // 更新小区显示（每一帧调用）
        if (beamCellManager.current) {
          beamCellManager.current.updateCells();
        }
      });

      // 监听2d切换事件
      viewer.sceneModePicker.viewModel.morphTo2D.afterExecute.addEventListener(
        () => {
          snow && viewer.scene.postProcessStages.remove(snow); // 移除
          rain && viewer.scene.postProcessStages.remove(rain); // 移除
          fog && viewer.scene.postProcessStages.remove(fog); // 移除
          setIsRotate(false);
          let layer = viewer.imageryLayers.get(0);
          layer["brightness"] = 1.5;
        }
      );
      viewer.sceneModePicker.viewModel.morphToColumbusView.afterExecute.addEventListener(
        () => {
          snow && viewer.scene.postProcessStages.remove(snow); // 移除
          rain && viewer.scene.postProcessStages.remove(rain); // 移除
          fog && viewer.scene.postProcessStages.remove(fog); // 移除
          setIsRotate(false);
          let layer = viewer.imageryLayers.get(0);
          layer["brightness"] = 1.5;
        }
      );

      viewer.sceneModePicker.viewModel.morphTo3D.afterExecute.addEventListener(
        () => {
          setTimeout(() => {
            setIsRotate(false);
            let layer = viewer.imageryLayers.get(0);
            layer["brightness"] = 1;
          }, 2000);
        }
      );
        $(".cesium-button").click(function(e){
          console.log('jQuery点击事件被触发', $(this));
          
          // 只处理样式，不阻止事件传播
          $(".cesium-button").removeClass("cesium-btn-selected");
          $(this).addClass("cesium-btn-selected");
          
          // 不要阻止事件继续冒泡到React的点击处理器
          // e.stopPropagation();
        });
        
        // 添加对波束尺寸按钮的特殊处理
        $(".cesium-button").each(function() {
          // console.log('检查按钮:', $(this).text());
          
          if($(this).text().includes('波束尺寸')) {
            // console.log('找到波束尺寸按钮');
            $(this).off('click').on('click', function(e) {
              // console.log('波束尺寸按钮被点击（jQuery处理）');
              $(".cesium-button").removeClass("cesium-btn-selected");
              $(this).addClass("cesium-btn-selected");
            });
          }
        });
        
    }
  }, [init]);

  // 设置处理的包装函数
  const settingDealWrapper = (settingName: string, settingValue: boolean) => {
    const settingParams: SettingDealParams = {
      viewer,
      satelliteListRef,
      setIsRotate
    };
    settingDeal(settingName, settingValue, settingParams);
  };

  // 地球旋转函数的包装函数
  const earthRotateWrapper = useCallback(() => {
    const earthRotateParams: EarthRotateParams = {
      viewer,
      previousTime: { current: previousTime }
    };
    earthRotate(earthRotateParams);
    previousTime = earthRotateParams.previousTime.current;
  }, []);

  // 获取并更新Cesium时间到store的函数
  const updateCesiumTimeToStore = useCallback(() => {
    if (viewer && viewer.clock) {
      // 使用Cesium的仿真时间
      const cesiumTime = viewer.clock.currentTime.toString();


      // 添加防护性检查
      if (!viewer.clock.currentTime || !viewer.clock.startTime) {
        console.warn('时钟时间无效，跳过时间差计算');
        return cesiumTime;
      }

      // 直接使用Cesium的JulianDate来计算时间差，避免Date转换的精度损失
      const currentTime = viewer.clock.currentTime;
      const startTime = viewer.clock.startTime;

      // 使用Cesium的secondsDifference方法获取秒级差值，然后转换为纳秒
      const secondsDiff = Cesium.JulianDate.secondsDifference(currentTime, startTime);

      // 检查计算结果是否有效
      if (isNaN(secondsDiff) || !isFinite(secondsDiff)) {
        console.warn('时间差计算结果无效:', secondsDiff);
        return cesiumTime;
      }

      // 将秒转换为纳秒 (1秒 = 1,000,000,000纳秒)
      // 如果您确实需要微秒级别，请使用 1,000,000
      const diff = Math.round(secondsDiff * 1000000000); // 纳秒级精度

      // 将diff值保存到simulationStore中
      useSimulationStore.getState().setTimeDiff(diff);

      // 打印计算出的diff值，而不是store中的timeDiff（因为store更新是异步的）
    

      setCesiumTime(cesiumTime);
      return cesiumTime;
    } 
    
    
    // else {
    //   // 后备使用系统时间
    //   const systemTime = new Date().toISOString();
    //   setCesiumTime(systemTime);
    //   return systemTime;
    // }
  }, [setCesiumTime]);



  // 坐标转换函数的包装函数
  const GetWGS84FromDKRWrapper = (coor: any, type: number) => {
    return GetWGS84FromDKR(coor, type);
  };

  // 经纬度转笛卡尔坐标的包装函数
  const wgs84ToCartesignWrapper = (lng: any, lat: any, alt: any) => {
    return wgs84ToCartesign(lng, lat, alt, viewer);
  };




  useEffect(()=>{
    settingDealWrapper(setting.currEdit?.name, setting.currEdit?.val);
  }, [setting])






  useEffect(()=>{
    if(situation.resource || situation.business){
      $("#cesiumContainer").hide();
    }else{
      $("#cesiumContainer").show();
    }

  },[situation])

  // 显示星座配置面板
  const showConstellationSettingPanel = () => {
    setIsConstellationSettingOpen(true);
  };

  // 关闭星座配置面板
  const closeConstellationSettingPanel = () => {
    setIsConstellationSettingOpen(false);
  };

  // 显示星座设计面板
  const showAddSatellitePanel = () => {
    setIsAddSatelliteOpen(true);
  };

  // 关闭星座设计面板
  const closeAddSatellitePanel = () => {
    setIsAddSatelliteOpen(false);
  };

  // 控制映射图弹窗显示
  const showMappingModal = () => setIsMappingModalOpen(true);
  const closeMappingModal = () => setIsMappingModalOpen(false);

  // 显示用户首选项面板
  const showUserPreferences = () => {
    setIsUserPreferencesOpen(true);
  };

  // 关闭用户首选项面板
  const closeUserPreferences = () => {
    setIsUserPreferencesOpen(false);
  };

  // 处理星座设计数据提交
  const handleAddSatelliteSubmit = (data: any) => {
    console.log('接收到星座设计数据:', data);

    if (data.type === 'file') {
      // 处理星座文件导入方式
      console.log('星座文件导入方式:', {
        name: data.constellationName,
        tleFile: data.tleFile,
        islFile: data.islFile
      });

      // TODO: 这里需要实现文件上传和处理逻辑
      // 可以调用后端API来处理TLE和ISL文件

    } else if (data.type === 'template') {
      // 处理星座模版构建方式
      console.log('星座模版构建方式:', {
        name: data.constellationName,
        orbitCount: data.orbitCount,
        satellitePerOrbit: data.satellitePerOrbit,
        altitude: data.altitude,
        inclination: data.inclination,
        meanMotion: data.meanMotion,
        beamRadius: data.beamRadius,
        islConfig: data.islConfig
      });

      // TODO: 这里需要实现模版构建逻辑
      // 可以调用后端API来根据参数生成星座

    } else if (data.type === 'scheduled') {
      // 处理定时接收方式
      console.log('定时接收方式:', data);

      // TODO: 这里需要实现定时接收逻辑

    } else if (data.type === 'groundStationFile') {
      // 处理地面站文件导入方式
      console.log('地面站文件导入方式:', data);

      // TODO: 这里需要实现地面站文件上传和处理逻辑

    } else if (data.type === 'groundStationTemplate') {
      // 处理地面站模版构建方式
      console.log('地面站模版构建方式:', {
        name: data.config.name,
        longitude: data.config.longitude,
        latitude: data.config.latitude,
        altitude: data.config.altitude
      });

      // TODO: 这里需要实现地面站模版构建逻辑

    } else if (data.type === 'customChannel') {
      // 处理自定义信道模型创建方式
      console.log('自定义信道模型创建方式:', {
        channelName: data.channelData.channel_name,
        pathLossExponent: data.channelData.path_loss_exponent,
        shadowingStd: data.channelData.shadowing_std,
        fadingFactor: data.channelData.fading_factor,
        additionalLoss: data.channelData.additional_loss,
        customParams: data.channelData.custom_params
      });

      // TODO: 这里需要实现自定义信道模型创建逻辑

    }

    // 可以在这里添加成功提示
    console.log('星座/地面站/信道模型创建请求已提交');
  };

  // 显示地面站设计面板
  const showAddGroundStationPanel = () => {
    setIsAddGroundStationOpen(true);
  };

  // 关闭地面站设计面板
  const closeAddGroundStationPanel = () => {
    setIsAddGroundStationOpen(false);
  };

  // 处理地面站设计数据提交
  const handleAddGroundStationSubmit = (data: any) => {
    console.log('接收到地面站设计数据:', data);
    
    if (data.type === 'file') {
      // 处理文件导入方式
      console.log('地面站文件导入方式:', {
        name: data.groundStationName,
        file: data.groundStationFile
      });
      
      // TODO: 这里需要实现地面站文件上传和处理逻辑
      // 可以调用后端API来处理地面站文件
      
    } else if (data.type === 'template') {
      // 处理模版构建方式
      console.log('地面站模版构建方式:', {
        name: data.config.name,
        longitude: data.config.longitude,
        latitude: data.config.latitude,
        altitude: data.config.altitude,
        description: data.config.description
      });
      
      // TODO: 这里需要实现地面站模版构建逻辑
      // 可以调用后端API来根据参数创建地面站
      
    }
    
    // 可以在这里添加成功提示
    console.log('地面站创建请求已提交');
  };

  // 处理卫星名称变化
  const handleSatelliteNameChange = (satelliteName: string) => {
    // console.log('接收到卫星名称变化:', satelliteName);
    setCurrentSatelliteName(satelliteName);
  };

  // 清除所有CZML数据源的包装函数
  const clearAllCzmlDataWrapper = () => {
    const clearParams: ClearCzmlDataParams = {
      viewer,
      setSatelliteList,
      setBaseStationList,
      setCurSatellite,
      setCurBaseStation,
      resetSimulation,
      beamCellManager
    };
    clearAllCzmlData(clearParams);
  };

  // 加载CZML数据的包装函数
  const loadCzmlDataWrapper = async (satelliteName: string, positionInfo?: PositionInfo) => {
    const loadParams: LoadCzmlDataParams = {
      viewer,
      czmlDataSource,
      setIsLoading,
      setSimulationConstellationName,
      setBaseStationList,
      setSatelliteList,
      setSimulationRunning
    };
    return loadCzmlData(satelliteName, loadParams, positionInfo);
  };

   useEffect(() => {
    // 组件加载后，设置定时器
    const intervalId = setInterval(() => {
      try {
        // 从 czmlDataSourceHelper 获取时钟对象

        // 健壮性检查，确保时钟对象存在
        if (!viewer || !viewer.clock || !viewer.clock.currentTime || !viewer.clock.startTime) {
          console.warn("时钟对象不存在，跳过本次执行");
          return;
        }

        // 直接使用Cesium的JulianDate来计算时间差，避免Date转换的精度损失
        const currentTime = viewer.clock.currentTime;
        const startTime = viewer.clock.startTime;

        // 使用Cesium的secondsDifference方法获取秒级差值，然后转换为纳秒
        const secondsDiff = Cesium.JulianDate.secondsDifference(currentTime, startTime);


        // const start = new Date(viewer.clock.currentTime);
        // const end = new Date(viewer.clock.startTime);
        // const diff = (start.getTime() - end.getTime())*1000000;

        // 检查计算结果是否有效
        if (isNaN(secondsDiff) || !isFinite(secondsDiff)) {
          console.warn("时间差计算结果无效，跳过本次执行");
          return;
        }

        // 将秒转换为纳秒 (1秒 = 1,000,000,000纳秒)
        // 如果您确实需要微秒级别，请使用 1,000,000
        const diff1 = Math.round(secondsDiff * 1000000000); // 纳秒级精度
        // console.log("diff1,diff",diff1,diff)

        // 使用 store 中的 timeDiff 而不是本地的 difftime
        if (diff1 !== null) {
          // postCurrentTime(useSimulationStore.getState().getTimeDiff())
          postCurrentTime(diff1)
            .then((response) => {
              // console.log("成功发送当前时间:", response);
            })
            .catch((error) => {
              console.error("发送当前时间失败:", error);
              // 即使API调用失败，也不影响定时器继续执行
            });
        }
      } catch (error) {
        // 捕获所有可能的错误，确保定时器不会停止
        console.error("定时器执行过程中发生错误:", error);
        // 定时器会继续执行，不会因为错误而停止
      }
    }, 500); // 每 500 毫秒执行一次

    // 关键一步：返回一个清理函数
    // 这个函数会在组件卸载时执行
    return () => {
      console.log("组件卸载，清除定时器。");
      clearInterval(intervalId); // 清除定时器，防止内存泄漏
    };
  }, []); // 空依赖数组 `[]` 确保此 effect 只在组件挂载和卸载时运行一次

  

  return (
    <>
      {/* Header组件 */}
      <Header
        setSituation={setSituation}
        showConstellationSettingPanel={showConstellationSettingPanel}
        satelliteAdd={showAddSatellitePanel}
        groundStationAdd={showAddGroundStationPanel}
        showMappingModal={showMappingModal}
        showUserPreferences={showUserPreferences}
      />
      <div
        id="cesiumContainer"
        style={{
          height: "100%",
          width: "100%",
          background: "#000",
        }}
      ></div>
      {
        (situation.satellite||situation.communicate) && (<>
          <div className="left-wrap">
            {/* <Box title="卫星数量统计图" component={<SatelliteBar/>} /> */}
            <Box title="卫星信息列表" component={<SatelliteInfoList satelliteList={satelliteListRef.current}/>}/>
            <Box title="网络状态" component={<SatelliteNumberChart />} />
          </div>
          <div className="right-wrap" style={{
            minWidth: 'max-content',
            right: '20px'
          }}>
            <Box
              title="卫星/地面站信息显示"
              component={
                <SatelliteInfo
                  // sateName={curSatellite}
                  // satelliteId={curSatellite}
                  sateName={curSatellite.split(' ').pop()}
                  satelliteId={curSatellite.split(' ').pop()}
                  launch={"2021-08"}
                  status={"service"}
                  activity={"stable"}
                  type={"satellite"}
                  constellationName={simulationConstellationName}
                />
              }
            />
            <Box 
              title="核心网日志"
              component={<CoreNetworkLogs/>}
              style={{
                minWidth: 'max-content',
                marginRight: '20px'
              }}
            />
          </div>
        </>)
      }
      

      
      {/* 全屏Loading动画 */}
      {isLoading && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 9999,
          color: 'white',
          fontSize: '18px'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            border: '4px solid rgba(255, 255, 255, 0.2)',
            borderTop: '4px solid #ffffff',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            marginBottom: '20px'
          }}></div>
          <div>正在加载数据...</div>
          <div style={{ fontSize: '14px', marginTop: '10px', opacity: 0.8 }}>
            请稍候，系统正在处理星座数据
          </div>
          
          {/* 添加CSS动画 */}
          <style dangerouslySetInnerHTML={{
            __html: `
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `
          }} />
        </div>
      )}
      
      {/* <Modal
        transitionName=""
        title="场景编辑"
        className="sceneEdit"
        visible={isModalOpen}
        onOk={closeSceneEditPanel}
        onCancel={closeSceneEditPanel}
      >
        <SettingPanel setSetting={setSetting} setting={setting} satelliteList={satelliteListRef.current} setSatelliteList={setSatelliteList} setScanes={setScenList}/>
      </Modal> */}
      
      {/* 添加星座配置面板Modal */}
      <Modal
        title="星座选择配置"
        className="sceneEdit"
        visible={isConstellationSettingOpen}
        onCancel={closeConstellationSettingPanel}
        footer={null}
        centered
      >
        <Settingsatellite 
          onClearCzmlData={clearAllCzmlDataWrapper}
          setting={setting} 
          setSetting={setSetting} 
          satelliteList={satelliteListRef.current} 
          setSatelliteList={setSatelliteList} 
          setScanes={setScenList}
          onSatelliteNameChange={handleSatelliteNameChange}
          onLoadCzmlData={loadCzmlDataWrapper}
          
        />
      </Modal>
      
      {/* 添加星座设计面板Modal */}
      <AddSatellite
        visible={isAddSatelliteOpen}
        onClose={closeAddSatellitePanel}
        onSubmit={handleAddSatelliteSubmit}
      />
      
      {/* 添加地面站设计面板Modal */}
      <AddGroundStation
        visible={isAddGroundStationOpen}
        onClose={closeAddGroundStationPanel}
        onSubmit={handleAddGroundStationSubmit}
      />
      
      {/* 映射图弹窗 */}
      <MappingGraph
        visible={isMappingModalOpen}
        onClose={closeMappingModal}
        networkModeConfig={networkModeConfig}
        kvmList={kvmList}
      />

      {/* 用户首选项面板Modal */}
      <UserPreferences
        visible={isUserPreferencesOpen}
        onClose={closeUserPreferences}
      />
      
      {/* <div id="left-border-line"></div>
      <div id="right-border-line"></div> */}
    </>
  );
};
export default CesiumComponent;
