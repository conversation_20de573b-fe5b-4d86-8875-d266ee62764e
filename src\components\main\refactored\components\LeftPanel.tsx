import React from 'react';
import Box from '../../box';
import SatelliteInfoList from '../../../left/satelliteInfoList/satelliteInfoList';
import SatelliteNumberChart from '../../../left/networkState/satelliteNumberChart';
import { CesiumViewerState, CesiumViewerActions } from '../hooks/useCesiumViewer';
import { SimulationState } from '../hooks/useSimulationState';

interface LeftPanelProps {
  cesiumState: CesiumViewerState & CesiumViewerActions;
  simulationState: SimulationState & any;
}

/**
 * 左侧面板组件
 * 负责渲染左侧的卫星信息和网络状态
 */
const LeftPanel: React.FC<LeftPanelProps> = ({ 
  cesiumState, 
  simulationState 
}) => {
  const { satelliteList } = cesiumState;

  return (
    <div className="left-wrap">
      <Box 
        title="卫星信息列表" 
        component={
          <SatelliteInfoList 
            satelliteList={satelliteList}
          />
        }
      />
      <Box 
        title="网络状态" 
        component={<SatelliteNumberChart />} 
      />
    </div>
  );
};

export default LeftPanel;
