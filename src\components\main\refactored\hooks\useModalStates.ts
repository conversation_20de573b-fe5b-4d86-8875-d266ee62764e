import { useState, useCallback } from 'react';

export interface ModalStates {
  isConstellationSettingOpen: boolean;
  isAddSatelliteOpen: boolean;
  isAddGroundStationOpen: boolean;
  isMappingModalOpen: boolean;
  isUserPreferencesOpen: boolean;
}

export interface ModalActions {
  showConstellationSettingPanel: () => void;
  closeConstellationSettingPanel: () => void;
  showAddSatellitePanel: () => void;
  closeAddSatellitePanel: () => void;
  showAddGroundStationPanel: () => void;
  closeAddGroundStationPanel: () => void;
  showMappingModal: () => void;
  closeMappingModal: () => void;
  showUserPreferences: () => void;
  closeUserPreferences: () => void;
  handleAddSatelliteSubmit: (data: any) => void;
  handleAddGroundStationSubmit: (data: any) => void;
}

/**
 * 弹窗状态管理Hook
 * 负责管理所有弹窗的显示/隐藏状态和相关操作
 */
export const useModalStates = (): ModalStates & ModalActions => {
  // 弹窗状态
  const [isConstellationSettingOpen, setIsConstellationSettingOpen] = useState<boolean>(false);
  const [isAddSatelliteOpen, setIsAddSatelliteOpen] = useState<boolean>(false);
  const [isAddGroundStationOpen, setIsAddGroundStationOpen] = useState<boolean>(false);
  const [isMappingModalOpen, setIsMappingModalOpen] = useState<boolean>(false);
  const [isUserPreferencesOpen, setIsUserPreferencesOpen] = useState<boolean>(false);

  // 星座配置面板操作
  const showConstellationSettingPanel = useCallback(() => {
    setIsConstellationSettingOpen(true);
  }, []);

  const closeConstellationSettingPanel = useCallback(() => {
    setIsConstellationSettingOpen(false);
  }, []);

  // 星座设计面板操作
  const showAddSatellitePanel = useCallback(() => {
    setIsAddSatelliteOpen(true);
  }, []);

  const closeAddSatellitePanel = useCallback(() => {
    setIsAddSatelliteOpen(false);
  }, []);

  // 地面站设计面板操作
  const showAddGroundStationPanel = useCallback(() => {
    setIsAddGroundStationOpen(true);
  }, []);

  const closeAddGroundStationPanel = useCallback(() => {
    setIsAddGroundStationOpen(false);
  }, []);

  // 映射图弹窗操作
  const showMappingModal = useCallback(() => {
    setIsMappingModalOpen(true);
  }, []);

  const closeMappingModal = useCallback(() => {
    setIsMappingModalOpen(false);
  }, []);

  // 用户首选项面板操作
  const showUserPreferences = useCallback(() => {
    setIsUserPreferencesOpen(true);
  }, []);

  const closeUserPreferences = useCallback(() => {
    setIsUserPreferencesOpen(false);
  }, []);

  // 处理星座设计数据提交
  const handleAddSatelliteSubmit = useCallback((data: any) => {
    console.log('接收到星座设计数据:', data);

    try {
      if (data.type === 'file') {
        // 处理星座文件导入方式
        console.log('星座文件导入方式:', {
          name: data.constellationName,
          tleFile: data.tleFile,
          islFile: data.islFile
        });
        // TODO: 实现文件上传和处理逻辑

      } else if (data.type === 'template') {
        // 处理星座模版构建方式
        console.log('星座模版构建方式:', {
          name: data.constellationName,
          orbitCount: data.orbitCount,
          satellitePerOrbit: data.satellitePerOrbit,
          altitude: data.altitude,
          inclination: data.inclination,
          meanMotion: data.meanMotion,
          beamRadius: data.beamRadius,
          islConfig: data.islConfig
        });
        // TODO: 实现模版构建逻辑

      } else if (data.type === 'scheduled') {
        // 处理定时接收方式
        console.log('定时接收方式:', data);
        // TODO: 实现定时接收逻辑

      } else if (data.type === 'groundStationFile') {
        // 处理地面站文件导入方式
        console.log('地面站文件导入方式:', data);
        // TODO: 实现地面站文件上传和处理逻辑

      } else if (data.type === 'groundStationTemplate') {
        // 处理地面站模版构建方式
        console.log('地面站模版构建方式:', {
          name: data.config.name,
          longitude: data.config.longitude,
          latitude: data.config.latitude,
          altitude: data.config.altitude
        });
        // TODO: 实现地面站模版构建逻辑

      } else if (data.type === 'customChannel') {
        // 处理自定义信道模型创建方式
        console.log('自定义信道模型创建方式:', {
          channelName: data.channelData.channel_name,
          pathLossExponent: data.channelData.path_loss_exponent,
          shadowingStd: data.channelData.shadowing_std,
          fadingFactor: data.channelData.fading_factor,
          additionalLoss: data.channelData.additional_loss,
          customParams: data.channelData.custom_params
        });
        // TODO: 实现自定义信道模型创建逻辑
      }

      console.log('星座/地面站/信道模型创建请求已提交');
      
      // 提交成功后关闭弹窗
      closeAddSatellitePanel();
      
    } catch (error) {
      console.error('处理星座设计数据时出错:', error);
      // 可以在这里添加错误提示
    }
  }, [closeAddSatellitePanel]);

  // 处理地面站设计数据提交
  const handleAddGroundStationSubmit = useCallback((data: any) => {
    console.log('接收到地面站设计数据:', data);
    
    try {
      if (data.type === 'file') {
        // 处理文件导入方式
        console.log('地面站文件导入方式:', {
          name: data.groundStationName,
          file: data.groundStationFile
        });
        // TODO: 实现地面站文件上传和处理逻辑
        
      } else if (data.type === 'template') {
        // 处理模版构建方式
        console.log('地面站模版构建方式:', {
          name: data.config.name,
          longitude: data.config.longitude,
          latitude: data.config.latitude,
          altitude: data.config.altitude,
          description: data.config.description
        });
        // TODO: 实现地面站模版构建逻辑
      }
      
      console.log('地面站创建请求已提交');
      
      // 提交成功后关闭弹窗
      closeAddGroundStationPanel();
      
    } catch (error) {
      console.error('处理地面站设计数据时出错:', error);
      // 可以在这里添加错误提示
    }
  }, [closeAddGroundStationPanel]);

  return {
    // 状态
    isConstellationSettingOpen,
    isAddSatelliteOpen,
    isAddGroundStationOpen,
    isMappingModalOpen,
    isUserPreferencesOpen,

    // 操作
    showConstellationSettingPanel,
    closeConstellationSettingPanel,
    showAddSatellitePanel,
    closeAddSatellitePanel,
    showAddGroundStationPanel,
    closeAddGroundStationPanel,
    showMappingModal,
    closeMappingModal,
    showUserPreferences,
    closeUserPreferences,
    handleAddSatelliteSubmit,
    handleAddGroundStationSubmit,
  };
};
