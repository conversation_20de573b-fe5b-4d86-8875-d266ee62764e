/**
 * 性能优化工具函数
 */

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return function executedFunction(this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 内存使用监控
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private intervalId: NodeJS.Timeout | null = null;
  private callbacks: Array<(info: MemoryInfo) => void> = [];

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  startMonitoring(interval: number = 5000): void {
    if (this.intervalId) return;

    this.intervalId = setInterval(() => {
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        this.callbacks.forEach(callback => callback(memInfo));
      }
    }, interval);
  }

  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  onMemoryUpdate(callback: (info: MemoryInfo) => void): () => void {
    this.callbacks.push(callback);
    
    // 返回取消订阅函数
    return () => {
      const index = this.callbacks.indexOf(callback);
      if (index > -1) {
        this.callbacks.splice(index, 1);
      }
    };
  }
}

interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

// 资源清理管理器
export class ResourceManager {
  private resources: Map<string, () => void> = new Map();

  register(key: string, cleanup: () => void): void {
    // 如果已存在同名资源，先清理旧的
    if (this.resources.has(key)) {
      const oldCleanup = this.resources.get(key);
      if (oldCleanup) {
        try {
          oldCleanup();
        } catch (error) {
          console.error(`清理资源 ${key} 时出错:`, error);
        }
      }
    }
    
    this.resources.set(key, cleanup);
  }

  unregister(key: string): void {
    const cleanup = this.resources.get(key);
    if (cleanup) {
      try {
        cleanup();
      } catch (error) {
        console.error(`清理资源 ${key} 时出错:`, error);
      }
      this.resources.delete(key);
    }
  }

  cleanupAll(): void {
    for (const [key, cleanup] of this.resources.entries()) {
      try {
        cleanup();
      } catch (error) {
        console.error(`清理资源 ${key} 时出错:`, error);
      }
    }
    this.resources.clear();
  }

  getResourceCount(): number {
    return this.resources.size;
  }

  listResources(): string[] {
    return Array.from(this.resources.keys());
  }
}

// 事件监听器管理器
export class EventListenerManager {
  private listeners: Map<string, {
    element: EventTarget;
    event: string;
    handler: EventListener;
    options?: boolean | AddEventListenerOptions;
  }> = new Map();

  add(
    key: string,
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ): void {
    // 如果已存在同名监听器，先移除旧的
    this.remove(key);
    
    element.addEventListener(event, handler, options);
    this.listeners.set(key, { element, event, handler, options });
  }

  remove(key: string): void {
    const listener = this.listeners.get(key);
    if (listener) {
      listener.element.removeEventListener(listener.event, listener.handler, listener.options);
      this.listeners.delete(key);
    }
  }

  removeAll(): void {
    for (const [key] of this.listeners.entries()) {
      this.remove(key);
    }
  }

  getListenerCount(): number {
    return this.listeners.size;
  }

  listListeners(): string[] {
    return Array.from(this.listeners.keys());
  }
}

// 定时器管理器
export class TimerManager {
  private timers: Map<string, NodeJS.Timeout> = new Map();
  private intervals: Map<string, NodeJS.Timeout> = new Map();

  setTimeout(key: string, callback: () => void, delay: number): void {
    // 如果已存在同名定时器，先清除旧的
    this.clearTimeout(key);
    
    const timerId = setTimeout(() => {
      callback();
      this.timers.delete(key);
    }, delay);
    
    this.timers.set(key, timerId);
  }

  setInterval(key: string, callback: () => void, interval: number): void {
    // 如果已存在同名定时器，先清除旧的
    this.clearInterval(key);
    
    const intervalId = setInterval(callback, interval);
    this.intervals.set(key, intervalId);
  }

  clearTimeout(key: string): void {
    const timerId = this.timers.get(key);
    if (timerId) {
      clearTimeout(timerId);
      this.timers.delete(key);
    }
  }

  clearInterval(key: string): void {
    const intervalId = this.intervals.get(key);
    if (intervalId) {
      clearInterval(intervalId);
      this.intervals.delete(key);
    }
  }

  clearAll(): void {
    // 清除所有定时器
    for (const [key] of this.timers.entries()) {
      this.clearTimeout(key);
    }
    
    // 清除所有间隔器
    for (const [key] of this.intervals.entries()) {
      this.clearInterval(key);
    }
  }

  getTimerCount(): number {
    return this.timers.size + this.intervals.size;
  }

  listTimers(): { timeouts: string[]; intervals: string[] } {
    return {
      timeouts: Array.from(this.timers.keys()),
      intervals: Array.from(this.intervals.keys())
    };
  }
}

// 性能监控工具
export class PerformanceMonitor {
  private marks: Map<string, number> = new Map();

  mark(name: string): void {
    this.marks.set(name, performance.now());
  }

  measure(name: string, startMark: string): number {
    const startTime = this.marks.get(startMark);
    if (startTime === undefined) {
      console.warn(`找不到起始标记: ${startMark}`);
      return 0;
    }
    
    const duration = performance.now() - startTime;
    console.log(`${name}: ${duration.toFixed(2)}ms`);
    return duration;
  }

  clearMarks(): void {
    this.marks.clear();
  }

  // 监控函数执行时间
  timeFunction<T extends (...args: any[]) => any>(
    name: string,
    func: T
  ): (...args: Parameters<T>) => ReturnType<T> {
    return (...args: Parameters<T>): ReturnType<T> => {
      const startTime = performance.now();
      const result = func(...args);
      const endTime = performance.now();
      console.log(`${name} 执行时间: ${(endTime - startTime).toFixed(2)}ms`);
      return result;
    };
  }
}
