/**
 * 重构组件的统一导出文件
 * 使用类型安全方案（declare const Cesium: any）
 */

// 主容器组件
export { default as MainContainer } from './MainContainer';
export { default as TestMain } from './TestMain';

// Hooks（推荐使用）
export { useCesiumViewer } from './hooks/useCesiumViewer';
export { useSimulationState } from './hooks/useSimulationState';
export { useModalStates } from './hooks/useModalStates';

// UI组件
export { default as CesiumViewport } from './components/CesiumViewport';
export { default as HeaderSection } from './components/HeaderSection';
export { default as LeftPanel } from './components/LeftPanel';
export { default as RightPanel } from './components/RightPanel';
export { default as LoadingOverlay } from './components/LoadingOverlay';
export { default as ModalsContainer } from './components/ModalsContainer';

// TypeScript类型定义
export type { CesiumViewerState, CesiumViewerActions } from './hooks/useCesiumViewer';
export type { SimulationState, SimulationActions } from './hooks/useSimulationState';
export type { ModalStates, ModalActions } from './hooks/useModalStates';

// 性能优化工具
export * from './utils/performanceUtils';

// 注意：使用任何导出的组件或Hook时，请在文件中添加：
// declare const Cesium: any;
