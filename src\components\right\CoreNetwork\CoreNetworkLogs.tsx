import React, { useState, useEffect } from 'react';
import { Tabs } from 'antd';
import './CoreNetworkLogs.css';
import { getCoreLog } from '../../utils/api/postAPI';
import useSimulationStore from '../../../store/simulationStore';

type LogData = {
  time: string;
  message: string;
  level: 'info' | 'warning' | 'error';
};

type LogType = 'AMF' | 'SMF' | 'UPF' | 'PCF' | 'UDM' | 'NRF';

const { TabPane } = Tabs;

// 解析日志行的通用函数
const parseLogLine = (line: string, nfType: string): LogData | null => {
  // 匹配格式: 05/27 03:38:15.429: [nf] LEVEL: message
  const regex = new RegExp(`^(\\d{2}\\/\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3}): \\[${nfType.toLowerCase()}\\] (\\w+): (.+)$`);
  const match = line.match(regex);
  
  if (!match) {
    // 如果不匹配标准格式，尝试其他常见格式
    const simpleRegex = /^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}|\d{2}\/\d{2} \d{2}:\d{2}:\d{2}): (.+)$/;
    const simpleMatch = line.match(simpleRegex);
    
    if (simpleMatch) {
      const [, timeStr, message] = simpleMatch;
      let level: 'info' | 'warning' | 'error' = 'info';
      
      // 根据消息内容判断日志级别
      const lowerMessage = message.toLowerCase();
      if (lowerMessage.includes('error') || lowerMessage.includes('failed') || lowerMessage.includes('失败')) {
        level = 'error';
      } else if (lowerMessage.includes('warning') || lowerMessage.includes('warn') || lowerMessage.includes('告警')) {
        level = 'warning';
      }
      
      return {
        time: formatLogTime(timeStr),
        message: message.trim(),
        level
      };
    }
    return null;
  }
  
  const [, timeStr, levelStr, message] = match;
  
  // 转换日志级别
  let level: 'info' | 'warning' | 'error' = 'info';
  if (levelStr.toLowerCase() === 'warning' || levelStr.toLowerCase() === 'warn') level = 'warning';
  if (levelStr.toLowerCase() === 'error') level = 'error';
  
  return {
    time: formatLogTime(timeStr),
    message: message.trim(),
    level
  };
};

// 格式化时间为更易读的格式
const formatLogTime = (timeStr: string): string => {
  // 如果已经是标准格式，直接返回
  if (timeStr.includes('-')) {
    return timeStr;
  }
  
  // 处理 MM/dd HH:mm:ss.SSS 格式
  const [datePart, timePart] = timeStr.split(' ');
  if (datePart && datePart.includes('/')) {
    const [month, day] = datePart.split('/');
    const currentYear = new Date().getFullYear();
    return `${currentYear}-${month.padStart(2, '0')}-${day.padStart(2, '0')} ${timePart}`;
  }
  
  return timeStr;
};

// 解析API返回的日志数据
const parseApiLogs = (apiResponse: any, nfType: string): LogData[] => {
  try {
    let logContent = '';
    
    // 处理不同的API响应格式
    if (typeof apiResponse === 'string') {
      logContent = apiResponse;
    } else if (apiResponse.logs) {
      logContent = apiResponse.logs;
    } else if (apiResponse.data) {
      logContent = apiResponse.data;
    } else {
      logContent = JSON.stringify(apiResponse);
    }
    
    // 分割日志行
    const lines = logContent.split('\n').filter(line => line.trim());
    
    // 解析并过滤有效的日志行，限制最多显示20条
    const logs = lines
      // .slice(-20) // 取最后20条（最新的日志）
      .map(line => parseLogLine(line, nfType))
      .filter((log): log is LogData => log !== null);
    
    // 如果没有解析出有效日志，创建一个默认日志条目
    if (logs.length === 0 && logContent.trim()) {
      const currentTime = new Date().toISOString().replace('T', ' ').slice(0, 19);
      logs.push({
        time: currentTime,
        message: logContent.slice(0, 200) + (logContent.length > 200 ? '...' : ''),
        level: 'info'
      });
    }
    
    return logs;
  } catch (error) {
    console.error(`解析${nfType}日志失败:`, error);
    return [];
  }
};

// 读取各种网络功能的日志
const loadCoreNetworkLogs = async (nfType: LogType): Promise<LogData[]> => {
  try {
    // console.log(`正在加载${nfType}日志...`);
    const response = await getCoreLog(nfType.toLowerCase());
    // console.log("日志log:"+response);
    const logs = parseApiLogs(response, nfType);
    
    // console.log(`${nfType}日志加载完成，共${logs.length}条`);
    return logs;
  } catch (error) {
    console.error(`读取${nfType}日志失败:`, error);
    
    // 返回错误信息作为日志条目
    const currentTime = new Date().toISOString().replace('T', ' ').slice(0, 19);
    return [{
      time: currentTime,
      message: `获取${nfType}日志失败: ${error instanceof Error ? error.message : '未知错误'}`,
      level: 'error'
    }];
  }
};



const CoreNetworkLogs: React.FC = () => {
  const [activeLogType, setActiveLogType] = useState<LogType>('AMF');
  const [logsData, setLogsData] = useState<Record<LogType, LogData[]>>({
    AMF: [],
    SMF: [],
    UPF: [],
    PCF: [],
    UDM: [],
    NRF: []
  });
  const [loading, setLoading] = useState<Record<LogType, boolean>>({
    AMF: false,
    SMF: false,
    UPF: false,
    PCF: false,
    UDM: false,
    NRF: false
  });
  // 加载指定类型的日志
  const loadLogsForType = async (logType: LogType) => {
    setLoading(prev => ({ ...prev, [logType]: true }));
    try {
      const logs = await loadCoreNetworkLogs(logType);
      setLogsData(prev => ({ ...prev, [logType]: logs }));
    } catch (error) {
      console.error(`加载${logType}日志失败:`, error);
    } finally {
      setLoading(prev => ({ ...prev, [logType]: false }));
    }
  };

  // 加载所有类型的日志
  const loadAllLogs = async () => {
    const logTypes: LogType[] = ['AMF', 'SMF', 'UPF', 'PCF', 'UDM', 'NRF'];
    
    // 并行加载所有日志
    const loadPromises = logTypes.map(logType => loadLogsForType(logType));
    await Promise.all(loadPromises);
  };

核心网日志  // 监听仿真状态，只在仿真运行时加载日志
  useEffect(() => {
    const simulationRunning = useSimulationStore.getState().simulationRunning;
    const isLoading = useSimulationStore.getState().isLoading;

    // 只有在仿真运行且不在加载状态时才加载日志
    if (simulationRunning && !isLoading) {
      loadAllLogs();
    }
  }, []);

  // 监听仿真状态变化
  useEffect(() => {
    const unsubscribe = useSimulationStore.subscribe((state) => {
      const { simulationRunning, isLoading } = state;

      // 当仿真开始运行且不在加载状态时，加载日志
      if (simulationRunning && !isLoading) {
        loadAllLogs();
      }
      // 当仿真停止时，清空日志数据
      else if (!simulationRunning) {
        setLogsData({
          AMF: [],
          SMF: [],
          UPF: [],
          PCF: [],
          UDM: [],
          NRF: []
        });
      }
    });

    return unsubscribe;
  }, []);

  const handleTabChange = (key: string) => {
    const newLogType = key as LogType;
    setActiveLogType(newLogType);
  };

  // 手动刷新当前标签页的日志
  const refreshCurrentLogs = () => {
    loadLogsForType(activeLogType);
  };

  // 获取当前标签页的日志数据
  const getCurrentLogs = (): LogData[] => {
    return logsData[activeLogType] || [];
  };

  const isCurrentLoading = loading[activeLogType];

  // 使用 useState 来跟踪仿真状态，确保组件能响应状态变化
  const [simulationRunning, setSimulationRunning] = useState(false);
  const [isSimulationLoading, setIsSimulationLoading] = useState(false);

  // 监听仿真状态变化并更新本地状态
  useEffect(() => {
    const unsubscribe = useSimulationStore.subscribe((state) => {
      setSimulationRunning(state.simulationRunning);
      setIsSimulationLoading(state.isLoading);
    });

    // 初始化状态
    const currentState = useSimulationStore.getState();
    setSimulationRunning(currentState.simulationRunning);
    setIsSimulationLoading(currentState.isLoading);

    return unsubscribe;
  }, []);

  // 定时刷新日志 - 每2秒更新一次
  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    if (simulationRunning && !isSimulationLoading) {
      // 仿真运行时，每2秒刷新一次日志
      intervalId = setInterval(() => {
        loadAllLogs();
      }, 2000); // 2秒 = 2000毫秒
    }

    // 清理定时器
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [simulationRunning, isSimulationLoading]);

  const needShow = simulationRunning && !isSimulationLoading;
  return (
    <div className="core-network-logs-wrapper">
        <div className="core-network-logs">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
          <Tabs onChange={handleTabChange} activeKey={activeLogType} type="card" className="log-tabs">
            <TabPane tab="AMF" key="AMF" />
            <TabPane tab="SMF" key="SMF" />
            <TabPane tab="UPF" key="UPF" />
            <TabPane tab="PCF" key="PCF" />
            <TabPane tab="UDM" key="UDM" />
            <TabPane tab="NRF" key="NRF" />
          </Tabs>
          {/* <button 
            onClick={refreshCurrentLogs}
            disabled={isCurrentLoading}
            style={{
              background: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '4px 8px',
              cursor: isCurrentLoading ? 'not-allowed' : 'pointer',
              fontSize: '12px',
              opacity: isCurrentLoading ? 0.6 : 1
            }}
          >
            {isCurrentLoading ? '刷新中...' : '刷新'}
          </button> */}
        </div>
        
        <div className="logs-container">
          {!needShow && (
            <div className="log-entry log-info">
            <span className="log-message">请开始仿真</span>
          </div>
          )}
          {needShow && (isCurrentLoading ? (
            <div className="log-entry log-info">
              <span className="log-message">正在加载{activeLogType}日志...</span>
            </div>
          ) : (
            getCurrentLogs().map((log, index) => (
              <div key={index} className={`log-entry log-${log.level}`}>
                <span className="log-time">{log.time}</span>
                <span className="log-message">{log.message}</span>
              </div>
            ))
          ))}
          {needShow && (!isCurrentLoading && getCurrentLogs().length === 0 && (
            <div className="log-entry log-warning">
              <span className="log-message">暂无{activeLogType}日志数据</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CoreNetworkLogs; 