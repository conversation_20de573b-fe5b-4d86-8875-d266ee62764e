import { LeftClickHandlerParams, RightClickHandlerParams, MouseMoveHandlerParams } from './types';
import { getProcessedLinkStatus, ProcessedLinkStatus } from './linkStatusAPI';

// 声明全局 Cesium 类型
declare const Cesium: any;

// 处理左键点击事件的函数
export const handleLeftClick = (params: LeftClickHandlerParams) => {
  const {
    viewer,
    setCurSatellite,
    nowPicksatellite,
    setPickedObject,
    beamCellManager,
    selectedSatelliteEntity
  } = params;

  return function (click: { position: any }) {
    var pick = viewer.scene.pick(click.position);
    if (pick && pick.id) {
      if(pick.id.name && (pick.id.name.includes("Satellite") || pick.id.name.includes("Ground")))
        setPickedObject(pick.id.id, pick.id.name);

      // 检查实体ID是否包含"Satellite/"前缀，表示这是一个卫星
      if (pick.id.name && pick.id.name.includes("Satellite/")) {
        const rawId = pick.id.name;
        const satelliteEntity = pick.id;

        try {
          // 总是先清除所有现有的波束和小区（包括其他卫星的）
          if (beamCellManager.current) {
            beamCellManager.current.clearAll();
          }

          // 清除selectedSatelliteEntity引用
          selectedSatelliteEntity.current = null;

          // 为新选中的卫星创建波束和小区
          // 存储选中的卫星实体引用，用于实时更新
          selectedSatelliteEntity.current = satelliteEntity;
          // 使用BeamCellManager创建波束和小区（内部计算所有参数）
          if (beamCellManager.current) {
            const success = beamCellManager.current.createBeamsAndCells(
              satelliteEntity.position,
              pick.id.id
            );

            if (success) {
              console.log(`波束和小区创建成功: ${rawId}`);
            } else {
              console.warn(`波束和小区创建失败: ${rawId}`);
            }
          } else {
            console.warn('BeamCellManager 不可用，无法创建波束和小区');
          }
        } catch (error) {
          console.error(`波束创建失败: ${(error as Error).message}`);
        }
        // 更新当前选中的卫星
        nowPicksatellite.current = [rawId, true, true];

        // 设置当前选中的卫星ID（使用完整ID）
        setCurSatellite(rawId);
      }
    }

    let cartesian3 = viewer.scene.camera.pickEllipsoid(
      click.position,
      viewer.scene.globe.ellipsoid
    );
    // 防止点击到地球之外报错，加个判断
    if (cartesian3 && Cesium.defined(cartesian3)) {
      let cartographic = Cesium.Cartographic.fromCartesian(cartesian3!);
      let lng = Cesium.Math.toDegrees(cartographic.longitude);
      let lat = Cesium.Math.toDegrees(cartographic.latitude);
      let height = cartographic.height;
      //23 28
      // wgs84ToCartesignWrapper(lng, lat, height)
      // 这些变量保留用于可能的未来功能扩展
      console.debug('地面点击坐标:', { lng, lat, height });
    }
  };
};

// 处理右键点击事件的函数
export const handleRightClick = (params: RightClickHandlerParams) => {
  const {
    viewer, // eslint-disable-line @typescript-eslint/no-unused-vars
    setNowSystemDate,
    setSatellitePostionData,
    nowPicksatellite,
    beamCellManager,
    selectedSatelliteEntity
  } = params;

  return function (click: { position: any }) {
    // 右键点击时无论点击什么都清除当前选中的卫星波束和小区
    // 保留参数引用以避免未使用变量警告
    void viewer; void click;
    console.log('右键点击事件触发');

    // 无论点击什么，都清除当前选中的卫星波束和小区
    try {
      // 清除相关数据
      setNowSystemDate([]);
      setSatellitePostionData([]);

      // 清除当前选中卫星的状态
      if (nowPicksatellite.current) {
        nowPicksatellite.current = [null, false, false];
      }

      // 使用BeamCellManager清除所有波束和小区显示
      if (beamCellManager.current) {
        beamCellManager.current.clearAll();
        console.log('通过BeamCellManager清除所有波束和小区');
      }

      // 清除选中的卫星实体引用
      if (selectedSatelliteEntity) {
        selectedSatelliteEntity.current = null;
        console.log('清除选中卫星实体引用');
      }

      console.log('右键点击清除完成');

    } catch (error) {
      console.error(`处理右键点击事件时出错: ${(error as Error).message}`);
    }
  };
};

// 全局变量用于管理悬停状态和定时器
let hoverTimer: NodeJS.Timeout | null = null;
let isHovering = false;
let currentEntity: any = null;

// 更新链路状态提示框的函数
const updateLinkStatusTooltip = async (entity: any, movement: { endPosition: any }, viewer: any) => {
  const tooltipElement = document.getElementById('linkInfoTooltip');
  if (!tooltipElement) return;

  try {
    // 获取链路状态数据
    const linkData: ProcessedLinkStatus = await getProcessedLinkStatus();

    // 更新提示框内容
    tooltipElement.innerHTML = `
      <div style="font-weight: bold; color: #00ff00; margin-bottom: 8px;">
        ${entity.linkType || '链路信息'}
      </div>

      <div style="margin-bottom: 4px;">
        <span style="color: #ffff00;">信号强度：</span>${linkData.signalStrength}
      </div>
      <div style="margin-bottom: 4px;">
        <span style="color: #ffff00;">链路负载：</span>${linkData.linkLoadPercentage}
      </div>
      <div style="margin-bottom: 4px;">
        <span style="color: #ffff00;">可用性：</span>
        <span style="color: ${linkData.linkStatusColor};">
          ${linkData.linkStatus}
        </span>
      </div>
    `;

    // 设置提示框位置
    const canvas = viewer.scene.canvas;
    const canvasRect = canvas.getBoundingClientRect();
    tooltipElement.style.left = (movement.endPosition.x + canvasRect.left + 10) + 'px';
    tooltipElement.style.top = (movement.endPosition.y + canvasRect.top - 10) + 'px';
    tooltipElement.style.display = 'block';

  } catch (error: any) {
    console.error('获取链路状态失败，使用默认值:', error);
    // 如果API调用失败，显示默认值
    tooltipElement.innerHTML = `
      <div style="font-weight: bold; color: #00ff00; margin-bottom: 8px;">
        ${entity.linkType || '链路信息'}
      </div>

      <div style="margin-bottom: 4px;">
        <span style="color: #ffff00;">信号强度：</span>N/A
      </div>
      <div style="margin-bottom: 4px;">
        <span style="color: #ffff00;">链路负载：</span>N/A
      </div>
      <div style="margin-bottom: 4px;">
        <span style="color: #ffff00;">可用性：</span>
        <span style="color: #888888;">
          N/A
        </span>
      </div>
    `;

    // 设置提示框位置
    const canvas = viewer.scene.canvas;
    const canvasRect = canvas.getBoundingClientRect();
    tooltipElement.style.left = (movement.endPosition.x + canvasRect.left + 10) + 'px';
    tooltipElement.style.top = (movement.endPosition.y + canvasRect.top - 10) + 'px';
    tooltipElement.style.display = 'block';
  }
};

// 处理鼠标移动事件的函数
export const handleMouseMove = (params: MouseMoveHandlerParams) => {
  const { viewer } = params;

  return function (movement: { endPosition: any }) {
    const pick = viewer.scene.pick(movement.endPosition);
    const infoDiv = document.getElementById('linkInfoTooltip');

    if (pick && pick.id) {
      // 检查是否悬停在polyline上（链路上）
      if (pick.id.polyline && (pick.id.linkLoad || pick.id.linkSpeed)) {
        const entity = pick.id;

        // 如果是新的实体或者之前没有悬停
        if (!isHovering || currentEntity !== entity) {
          // 清除之前的定时器
          if (hoverTimer) {
            clearInterval(hoverTimer);
            hoverTimer = null;
          }

          isHovering = true;
          currentEntity = entity;

          // 立即获取一次数据
          updateLinkStatusTooltip(entity, movement, viewer);

          // 设置定时器，每2秒更新一次
          hoverTimer = setInterval(() => {
            if (isHovering && currentEntity === entity) {
              updateLinkStatusTooltip(entity, movement, viewer);
            }
          }, 200);
        }

        // 创建提示框（如果不存在）
        if (!infoDiv) {
          const tooltip = document.createElement('div');
          tooltip.id = 'linkInfoTooltip';
          tooltip.style.position = 'absolute';
          tooltip.style.background = 'rgba(0, 0, 0, 0.8)';
          tooltip.style.color = 'white';
          tooltip.style.padding = '10px';
          tooltip.style.borderRadius = '5px';
          tooltip.style.fontSize = '12px';
          tooltip.style.zIndex = '10000';
          tooltip.style.pointerEvents = 'none';
          tooltip.style.maxWidth = '200px';
          tooltip.style.border = '1px solid #00ff00';
          tooltip.style.boxShadow = '0 2px 10px rgba(0, 255, 0, 0.3)';
          document.body.appendChild(tooltip);
        }
      } else {
        // 如果没有悬停在polyline上，清除定时器并隐藏提示框
        if (hoverTimer) {
          clearInterval(hoverTimer);
          hoverTimer = null;
        }
        isHovering = false;
        currentEntity = null;

        if (infoDiv) {
          infoDiv.style.display = 'none';
        }
      }
    } else {
      // 如果没有pick到任何对象，清除定时器并隐藏提示框
      if (hoverTimer) {
        clearInterval(hoverTimer);
        hoverTimer = null;
      }
      isHovering = false;
      currentEntity = null;

      if (infoDiv) {
        infoDiv.style.display = 'none';
      }
    }
  };
};